-- Create a test admin user with known password
-- Password: admin123 (hashed with bcrypt)

-- First, let's create a user with a known password hash
-- This is the bcrypt hash for "admin123" with 12 rounds
INSERT INTO users (id, email, password_hash, email_confirmed, created_at, updated_at)
VALUES 
('11111111-1111-1111-1111-111111111111', '<EMAIL>', '$2b$12$LQv3c1yqBw2fyuQjSeLKKOHxYqGqfqvqrNf3i2/b8/FA2l1Sg8h.S', true, now(), now())
ON CONFLICT (email) DO UPDATE SET 
  password_hash = '$2b$12$LQv3c1yqBw2fyuQjSeLKKOHxYqGqfqvqrNf3i2/b8/FA2l1Sg8h.S',
  email_confirmed = true;

-- Create the profile for this user
INSERT INTO profiles (id, first_name, last_name, email, role, created_at, updated_at)
VALUES 
('11111111-1111-1111-1111-111111111111', 'Test', 'Admin', '<EMAIL>', 'admin', now(), now())
ON CONFLICT (id) DO UPDATE SET 
  role = 'admin',
  first_name = 'Test',
  last_name = 'Admin',
  email = '<EMAIL>';

-- Verify the user was created
SELECT u.id, u.email, u.email_confirmed, p.role 
FROM users u 
LEFT JOIN profiles p ON u.id = p.id 
WHERE u.email = '<EMAIL>';
