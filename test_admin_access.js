// Test script to verify admin access after permission fix
import axios from 'axios';

const BASE_URL = 'http://localhost:3001/api/v1';

async function testAdminAccess() {
  try {
    console.log('🧪 Testing Admin Access After Permission Fix\n');

    // Test 1: Try to login with admin user
    console.log('1. Testing admin login...');
    
    // First, let's try to register a test admin user
    try {
      const registerResponse = await axios.post(`${BASE_URL}/auth/register`, {
        email: '<EMAIL>',
        password: 'admin123',
        firstName: 'Test',
        lastName: 'Admin',
        role: 'admin'
      });
      console.log('✅ Test admin user created successfully');
    } catch (error) {
      if (error.response?.data?.error?.includes('already exists')) {
        console.log('ℹ️  Test admin user already exists');
      } else {
        console.log('❌ Failed to create test admin user:', error.response?.data?.error || error.message);
      }
    }

    // Now try to login
    let token;
    try {
      const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
        email: '<EMAIL>',
        password: 'admin123'
      });
      
      token = loginResponse.data.access_token;
      console.log('✅ Admin login successful');
      console.log(`   User: ${loginResponse.data.user.email}`);
      console.log(`   Role: ${loginResponse.data.user.profile?.role}`);
    } catch (error) {
      console.log('❌ Admin login failed:', error.response?.data?.error || error.message);
      return;
    }

    // Test 2: Access admin-only endpoints
    console.log('\n2. Testing admin-only endpoints...');
    
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };

    // Test admin users endpoint
    try {
      const usersResponse = await axios.get(`${BASE_URL}/admin/users`, { headers });
      console.log('✅ Admin users endpoint accessible');
      console.log(`   Found ${usersResponse.data.users?.length || 0} users`);
    } catch (error) {
      console.log('❌ Admin users endpoint failed:', error.response?.data?.error || error.message);
    }

    // Test admin stats endpoint
    try {
      const statsResponse = await axios.get(`${BASE_URL}/admin/stats`, { headers });
      console.log('✅ Admin stats endpoint accessible');
      console.log(`   Total users: ${statsResponse.data.statistics?.totalUsers || 0}`);
    } catch (error) {
      console.log('❌ Admin stats endpoint failed:', error.response?.data?.error || error.message);
    }

    // Test 3: Access management endpoints that require permissions
    console.log('\n3. Testing management endpoints with permissions...');

    // Test departments endpoint
    try {
      const deptResponse = await axios.get(`${BASE_URL}/management/departments`, { headers });
      console.log('✅ Departments endpoint accessible');
      console.log(`   Found ${deptResponse.data.departments?.length || 0} departments`);
    } catch (error) {
      console.log('❌ Departments endpoint failed:', error.response?.data?.error || error.message);
    }

    // Test roles endpoint
    try {
      const rolesResponse = await axios.get(`${BASE_URL}/management/roles`, { headers });
      console.log('✅ Roles endpoint accessible');
      console.log(`   Found ${rolesResponse.data.roles?.length || 0} roles`);
    } catch (error) {
      console.log('❌ Roles endpoint failed:', error.response?.data?.error || error.message);
    }

    // Test permissions endpoint
    try {
      const permResponse = await axios.get(`${BASE_URL}/permissions/available`, { headers });
      console.log('✅ Permissions endpoint accessible');
      console.log(`   Available permission groups: ${Object.keys(permResponse.data.permissionGroups || {}).length}`);
    } catch (error) {
      console.log('❌ Permissions endpoint failed:', error.response?.data?.error || error.message);
    }

    console.log('\n🎉 Admin access testing completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testAdminAccess();
