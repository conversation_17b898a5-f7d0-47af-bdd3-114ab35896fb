-- Create missing roles
INSERT INTO roles (name, description, permissions, is_active) VALUES 
('doctor', 'Medical Doctor', '[]'::jsonb, true),
('nurse', 'Registered Nurse', '[]'::jsonb, true),
('pharmacist', 'Licensed Pharmacist', '[]'::jsonb, true),
('technician', 'Medical Technician', '[]'::jsonb, true),
('receptionist', 'Front Desk Receptionist', '[]'::jsonb, true);

-- Now update all roles with proper permissions
UPDATE roles 
SET permissions = '[
  "user:read",
  "user:update", 
  "user:status_change",
  "employee:create",
  "employee:read",
  "employee:update",
  "employee:delete",
  "employee:bulk",
  "department:read",
  "position:read",
  "role:read",
  "schedule:create",
  "schedule:read",
  "schedule:update",
  "schedule:delete",
  "schedule:assign",
  "leave:create",
  "leave:read",
  "leave:update",
  "leave:delete",
  "leave:approve",
  "reports:view",
  "reports:export"
]'::jsonb
WHERE name = 'manager';

UPDATE roles 
SET permissions = '[
  "employee:read",
  "schedule:read",
  "leave:create",
  "leave:read",
  "leave:update",
  "reports:view"
]'::jsonb
WHERE name = 'doctor';

UPDATE roles 
SET permissions = '[
  "employee:read",
  "schedule:read",
  "leave:create",
  "leave:read",
  "leave:update"
]'::jsonb
WHERE name = 'nurse';

UPDATE roles 
SET permissions = '[
  "employee:read",
  "schedule:read",
  "leave:create",
  "leave:read",
  "leave:update"
]'::jsonb
WHERE name = 'pharmacist';

UPDATE roles 
SET permissions = '[
  "employee:read",
  "schedule:read",
  "leave:create",
  "leave:read",
  "leave:update"
]'::jsonb
WHERE name = 'technician';

UPDATE roles 
SET permissions = '[
  "employee:read",
  "schedule:read",
  "leave:create",
  "leave:read",
  "leave:update"
]'::jsonb
WHERE name = 'receptionist';

-- Show all roles
SELECT name, jsonb_array_length(permissions) as permission_count, is_active FROM roles ORDER BY name;
