import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { useUserManagement } from '@/hooks/useUserManagement';
import { 
  Activity, 
  Search, 
  Filter, 
  RefreshCw, 
  Calendar,
  User,
  Shield,
  Database,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';

const actionLabels = {
  login: 'Login',
  logout: 'Logout',
  user_create: 'Buat User',
  user_update: 'Update User',
  user_delete: 'Hapus User',
  user_suspend: 'Tangguhkan User',
  user_activate: 'Aktifkan User',
  password_reset: 'Reset Password',
  admin_access: 'Akses Admin',
  system_config: 'Konfigurasi Sistem',
};

const actionColors = {
  login: 'bg-green-100 text-green-800',
  logout: 'bg-gray-100 text-gray-800',
  user_create: 'bg-blue-100 text-blue-800',
  user_update: 'bg-yellow-100 text-yellow-800',
  user_delete: 'bg-red-100 text-red-800',
  user_suspend: 'bg-red-100 text-red-800',
  user_activate: 'bg-green-100 text-green-800',
  password_reset: 'bg-purple-100 text-purple-800',
  admin_access: 'bg-orange-100 text-orange-800',
  system_config: 'bg-indigo-100 text-indigo-800',
};

const resourceLabels = {
  user: 'Pengguna',
  system: 'Sistem',
  employee: 'Karyawan',
  department: 'Departemen',
  role: 'Role',
  schedule: 'Jadwal',
  leave_request: 'Cuti',
};

export function ActivityLogs() {
  const [filters, setFilters] = useState({
    userId: '',
    action: '',
    resource: '',
    startDate: '',
    endDate: '',
    limit: 50,
    offset: 0,
  });

  const { useActivityLogs } = useUserManagement();
  const { data: logs, isLoading, refetch } = useActivityLogs(filters);

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value, offset: 0 }));
  };

  const handlePageChange = (direction: 'prev' | 'next') => {
    setFilters(prev => ({
      ...prev,
      offset: direction === 'next' 
        ? prev.offset + prev.limit 
        : Math.max(0, prev.offset - prev.limit)
    }));
  };

  const clearFilters = () => {
    setFilters({
      userId: '',
      action: '',
      resource: '',
      startDate: '',
      endDate: '',
      limit: 50,
      offset: 0,
    });
  };

  const getActionIcon = (action: string) => {
    if (action.includes('login')) return User;
    if (action.includes('admin')) return Shield;
    if (action.includes('system')) return Database;
    return Activity;
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filter Log Aktivitas
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="userId">User ID</Label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="userId"
                  placeholder="Cari berdasarkan User ID..."
                  value={filters.userId}
                  onChange={(e) => handleFilterChange('userId', e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="action">Aksi</Label>
              <Select value={filters.action} onValueChange={(value) => handleFilterChange('action', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Pilih aksi" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Aksi</SelectItem>
                  {Object.entries(actionLabels).map(([value, label]) => (
                    <SelectItem key={value} value={value}>{label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="resource">Resource</Label>
              <Select value={filters.resource} onValueChange={(value) => handleFilterChange('resource', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Pilih resource" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Resource</SelectItem>
                  {Object.entries(resourceLabels).map(([value, label]) => (
                    <SelectItem key={value} value={value}>{label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="startDate">Tanggal Mulai</Label>
              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="startDate"
                  type="date"
                  value={filters.startDate}
                  onChange={(e) => handleFilterChange('startDate', e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="endDate">Tanggal Selesai</Label>
              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  id="endDate"
                  type="date"
                  value={filters.endDate}
                  onChange={(e) => handleFilterChange('endDate', e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="limit">Jumlah per halaman</Label>
              <Select value={filters.limit.toString()} onValueChange={(value) => handleFilterChange('limit', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="25">25</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                  <SelectItem value="200">200</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex gap-2 mt-4">
            <Button variant="outline" onClick={() => refetch()}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button variant="outline" onClick={clearFilters}>
              Clear Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Activity Logs Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Log Aktivitas
          </CardTitle>
        </CardHeader>
        <CardContent>
          {logs && logs.length > 0 ? (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Waktu</TableHead>
                    <TableHead>User</TableHead>
                    <TableHead>Aksi</TableHead>
                    <TableHead>Resource</TableHead>
                    <TableHead>Detail</TableHead>
                    <TableHead>IP Address</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {logs.map((log: any, index: number) => {
                    const ActionIcon = getActionIcon(log.action);
                    return (
                      <TableRow key={index}>
                        <TableCell>
                          <div className="text-sm">
                            {new Date(log.created_at).toLocaleString('id-ID')}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            <div className="font-medium">{log.user_email || 'System'}</div>
                            {log.user_id && (
                              <div className="text-xs text-gray-500">{log.user_id}</div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={actionColors[log.action as keyof typeof actionColors] || 'bg-gray-100 text-gray-800'}>
                            <ActionIcon className="h-3 w-3 mr-1" />
                            {actionLabels[log.action as keyof typeof actionLabels] || log.action}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm">
                            {resourceLabels[log.resource as keyof typeof resourceLabels] || log.resource}
                          </span>
                          {log.resource_id && (
                            <div className="text-xs text-gray-500">{log.resource_id}</div>
                          )}
                        </TableCell>
                        <TableCell>
                          {log.details ? (
                            <div className="text-sm max-w-xs truncate" title={JSON.stringify(log.details)}>
                              {typeof log.details === 'string' ? log.details : JSON.stringify(log.details)}
                            </div>
                          ) : (
                            <span className="text-gray-400">-</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <span className="text-sm font-mono">
                            {log.ip_address || '-'}
                          </span>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>

              {/* Pagination */}
              <div className="flex items-center justify-between mt-4">
                <div className="text-sm text-gray-500">
                  Menampilkan {filters.offset + 1} - {filters.offset + logs.length} dari total log
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange('prev')}
                    disabled={filters.offset === 0}
                  >
                    <ChevronLeft className="h-4 w-4" />
                    Sebelumnya
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePageChange('next')}
                    disabled={logs.length < filters.limit}
                  >
                    Selanjutnya
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </>
          ) : (
            <div className="text-center py-8">
              <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">Tidak ada log aktivitas yang ditemukan</p>
              <p className="text-sm text-gray-400 mt-1">
                Coba ubah filter atau refresh halaman
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
