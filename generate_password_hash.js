// Generate password hash using the same bcrypt settings as the backend
import bcrypt from 'bcryptjs';

const BCRYPT_ROUNDS = 12;
const password = 'admin123';

async function generateHash() {
  try {
    console.log('Generating hash for password:', password);
    const hash = await bcrypt.hash(password, BCRYPT_ROUNDS);
    console.log('Generated hash:', hash);

    // Verify the hash works
    const isValid = await bcrypt.compare(password, hash);
    console.log('Hash verification successful:', isValid);

    // Output SQL to update the user
    console.log('\nSQL to update user:');
    console.log(`UPDATE users SET password_hash = '${hash}' WHERE email = '<EMAIL>';`);
  } catch (error) {
    console.error('Error generating hash:', error);
  }
}

generateHash();
