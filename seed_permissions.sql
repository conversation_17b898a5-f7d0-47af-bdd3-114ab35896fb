-- Comprehensive Permission Seeding <PERSON>ript
-- This script ensures all roles have proper permissions to prevent future permission issues

-- First, ensure all required roles exist
INSERT INTO roles (name, description, permissions, is_active) VALUES
('admin', 'System Administrator', '[]'::jsonb, true),
('manager', 'Department Manager', '[]'::jsonb, true),
('doctor', 'Medical Doctor', '[]'::jsonb, true),
('nurse', 'Registered Nurse', '[]'::jsonb, true),
('pharmacist', 'Licensed Pharmacist', '[]'::jsonb, true),
('technician', 'Medical Technician', '[]'::jsonb, true),
('receptionist', 'Front Desk Receptionist', '[]'::jsonb, true)
ON CONFLICT (name) DO NOTHING;

-- Update admin role with full permissions
UPDATE roles 
SET permissions = '[
  "user:create",
  "user:read",
  "user:update",
  "user:delete",
  "user:bulk",
  "user:password_reset",
  "user:status_change",
  "employee:create",
  "employee:read",
  "employee:update",
  "employee:delete",
  "employee:bulk",
  "department:create",
  "department:read",
  "department:update",
  "department:delete",
  "role:create",
  "role:read",
  "role:update",
  "role:delete",
  "position:create",
  "position:read",
  "position:update",
  "position:delete",
  "schedule:create",
  "schedule:read",
  "schedule:update",
  "schedule:delete",
  "schedule:assign",
  "leave:create",
  "leave:read",
  "leave:update",
  "leave:delete",
  "leave:approve",
  "system:admin",
  "system:stats",
  "system:logs",
  "system:backup",
  "reports:view",
  "reports:export",
  "reports:analytics"
]'::jsonb
WHERE name = 'admin';

-- Update manager role with management permissions
UPDATE roles 
SET permissions = '[
  "user:read",
  "user:update",
  "user:status_change",
  "employee:create",
  "employee:read",
  "employee:update",
  "employee:delete",
  "employee:bulk",
  "department:read",
  "position:read",
  "role:read",
  "schedule:create",
  "schedule:read",
  "schedule:update",
  "schedule:delete",
  "schedule:assign",
  "leave:create",
  "leave:read",
  "leave:update",
  "leave:delete",
  "leave:approve",
  "reports:view",
  "reports:export"
]'::jsonb
WHERE name = 'manager';

-- Update doctor role with medical staff permissions
UPDATE roles 
SET permissions = '[
  "employee:read",
  "schedule:read",
  "leave:create",
  "leave:read",
  "leave:update",
  "reports:view"
]'::jsonb
WHERE name = 'doctor';

-- Update nurse role with nursing staff permissions
UPDATE roles 
SET permissions = '[
  "employee:read",
  "schedule:read",
  "leave:create",
  "leave:read",
  "leave:update"
]'::jsonb
WHERE name = 'nurse';

-- Update pharmacist role with pharmacy permissions
UPDATE roles 
SET permissions = '[
  "employee:read",
  "schedule:read",
  "leave:create",
  "leave:read",
  "leave:update"
]'::jsonb
WHERE name = 'pharmacist';

-- Update technician role with technical staff permissions
UPDATE roles 
SET permissions = '[
  "employee:read",
  "schedule:read",
  "leave:create",
  "leave:read",
  "leave:update"
]'::jsonb
WHERE name = 'technician';

-- Update receptionist role with front desk permissions
UPDATE roles 
SET permissions = '[
  "employee:read",
  "schedule:read",
  "leave:create",
  "leave:read",
  "leave:update"
]'::jsonb
WHERE name = 'receptionist';

-- Create a default admin user if none exists
DO $$
DECLARE
    admin_count INTEGER;
    new_user_id UUID;
BEGIN
    -- Check if any admin users exist
    SELECT COUNT(*) INTO admin_count 
    FROM users u 
    JOIN profiles p ON u.id = p.id 
    WHERE p.role = 'admin';
    
    -- If no admin users exist, create one
    IF admin_count = 0 THEN
        -- Generate a new UUID for the user
        new_user_id := gen_random_uuid();
        
        -- Create the admin user
        INSERT INTO users (id, email, password_hash, email_confirmed, created_at, updated_at)
        VALUES (
            new_user_id,
            '<EMAIL>',
            '$2b$12$LQv3c1yqBw2fyuQjSeLKKOHxYqGqfqvqrNf3i2/b8/FA2l1Sg8h.S', -- password: admin123
            true,
            NOW(),
            NOW()
        );
        
        -- Create the admin profile
        INSERT INTO profiles (id, first_name, last_name, email, role, created_at, updated_at)
        VALUES (
            new_user_id,
            'System',
            'Administrator',
            '<EMAIL>',
            'admin',
            NOW(),
            NOW()
        );
        
        RAISE NOTICE 'Created default admin user: <EMAIL> (password: admin123)';
    END IF;
END $$;

-- Verify the setup
SELECT 
    r.name as role_name,
    jsonb_array_length(r.permissions) as permission_count,
    r.is_active,
    COUNT(p.id) as user_count
FROM roles r
LEFT JOIN profiles p ON p.role = r.name
GROUP BY r.name, r.permissions, r.is_active
ORDER BY r.name;

-- Show admin users
SELECT 
    u.email,
    p.first_name,
    p.last_name,
    p.role,
    u.email_confirmed
FROM users u
JOIN profiles p ON u.id = p.id
WHERE p.role = 'admin';

COMMIT;
