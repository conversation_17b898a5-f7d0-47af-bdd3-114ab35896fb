// Management routes for departments, roles, and positions
import { Router, Response } from 'express';
import { ManagementService } from '@/services/managementService';
import { authenticateToken, AuthRequest } from '@/middleware/auth';
import { requirePermission } from '@/middleware/permissions';
import { PERMISSIONS } from '@/services/permissionService';
import { validate, departmentSchemas, roleSchemas } from '@/middleware/validation';

const router = Router();

// Apply authentication to all routes
router.use(authenticateToken);

// DEPARTMENT ROUTES

// Get all departments
router.get('/departments', requirePermission(PERMISSIONS.DEPARTMENT_READ), async (req: AuthRequest, res: Response) => {
  try {
    const departments = await ManagementService.getAllDepartments();
    res.json({ departments });
  } catch (error) {
    console.error('Get departments endpoint error:', error);
    res.status(500).json({ error: 'Failed to fetch departments' });
  }
});

// Get department by ID
router.get('/departments/:id', async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const department = await ManagementService.getDepartmentById(id);
    
    if (!department) {
      return res.status(404).json({ error: 'Department not found' });
    }

    res.json({ department });
  } catch (error) {
    console.error('Get department by ID endpoint error:', error);
    res.status(500).json({ error: 'Failed to fetch department' });
  }
});

// Create department (Admin only)
router.post('/departments', requirePermission(PERMISSIONS.DEPARTMENT_CREATE), validate(departmentSchemas.create), async (req: AuthRequest, res: Response) => {
  try {
    const departmentData = {
      name: req.body.name,
      description: req.body.description,
      manager_id: req.body.managerId,
      is_active: true,
    };

    const department = await ManagementService.createDepartment(departmentData);

    res.status(201).json({
      message: 'Department created successfully',
      department
    });
  } catch (error) {
    console.error('Create department endpoint error:', error);
    
    if (error instanceof Error && error.message.includes('already exists')) {
      return res.status(409).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Failed to create department' });
  }
});

// Update department (Admin only)
router.put('/departments/:id', requirePermission(PERMISSIONS.DEPARTMENT_UPDATE), validate(departmentSchemas.update), async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    
    const updateData: any = {};
    if (req.body.name !== undefined) updateData.name = req.body.name;
    if (req.body.description !== undefined) updateData.description = req.body.description;
    if (req.body.managerId !== undefined) updateData.manager_id = req.body.managerId;
    if (req.body.isActive !== undefined) updateData.is_active = req.body.isActive;

    // department id
    if (req.body.departmentId !== undefined) updateData.department_id = req.body.departmentId;

    const department = await ManagementService.updateDepartment(id, updateData);

    res.json({
      message: 'Department updated successfully',
      department
    });
  } catch (error) {
    console.error('Update department endpoint error:', error);
    
    if (error instanceof Error) {
      if (error.message === 'Department not found') {
        return res.status(404).json({ error: error.message });
      }
      if (error.message.includes('already exists')) {
        return res.status(409).json({ error: error.message });
      }
    }
    
    res.status(500).json({ error: 'Failed to update department' });
  }
});

// Delete department (Admin only)
router.delete('/departments/:id', requirePermission(PERMISSIONS.DEPARTMENT_DELETE), async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    await ManagementService.deleteDepartment(id);
    res.json({ message: 'Department deleted successfully' });
  } catch (error) {
    console.error('Delete department endpoint error:', error);
    
    if (error instanceof Error && error.message === 'Department not found') {
      return res.status(404).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Failed to delete department' });
  }
});

// ROLE ROUTES

// Get all roles
router.get('/roles', requirePermission(PERMISSIONS.ROLE_READ), async (req: AuthRequest, res: Response) => {
  try {
    const roles = await ManagementService.getAllRoles();
    res.json({ roles });
  } catch (error) {
    console.error('Get roles endpoint error:', error);
    res.status(500).json({ error: 'Failed to fetch roles' });
  }
});

// Get role by ID
router.get('/roles/:id', async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const role = await ManagementService.getRoleById(id);
    
    if (!role) {
      return res.status(404).json({ error: 'Role not found' });
    }

    res.json({ role });
  } catch (error) {
    console.error('Get role by ID endpoint error:', error);
    res.status(500).json({ error: 'Failed to fetch role' });
  }
});

// Create role (Admin only)
router.post('/roles', requirePermission(PERMISSIONS.ROLE_CREATE), validate(roleSchemas.create), async (req: AuthRequest, res: Response) => {
  try {
    const roleData = {
      name: req.body.name,
      description: req.body.description,
      permissions: req.body.permissions,
      is_active: true,
    };

    const role = await ManagementService.createRole(roleData);

    res.status(201).json({
      message: 'Role created successfully',
      role
    });
  } catch (error) {
    console.error('Create role endpoint error:', error);
    
    if (error instanceof Error && error.message.includes('already exists')) {
      return res.status(409).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Failed to create role' });
  }
});

// Update role (Admin only)
router.put('/roles/:id', requirePermission(PERMISSIONS.ROLE_UPDATE), validate(roleSchemas.update), async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    
    const updateData: any = {};
    if (req.body.name !== undefined) updateData.name = req.body.name;
    if (req.body.description !== undefined) updateData.description = req.body.description;
    if (req.body.permissions !== undefined) updateData.permissions = req.body.permissions;
    if (req.body.isActive !== undefined) updateData.is_active = req.body.isActive;

    const role = await ManagementService.updateRole(id, updateData);

    res.json({
      message: 'Role updated successfully',
      role
    });
  } catch (error) {
    console.error('Update role endpoint error:', error);
    
    if (error instanceof Error) {
      if (error.message === 'Role not found') {
        return res.status(404).json({ error: error.message });
      }
      if (error.message.includes('already exists')) {
        return res.status(409).json({ error: error.message });
      }
    }
    
    res.status(500).json({ error: 'Failed to update role' });
  }
});

// POSITION ROUTES

// Get all positions
router.get('/positions', requirePermission(PERMISSIONS.POSITION_READ), async (req: AuthRequest, res: Response) => {
  try {
    const positions = await ManagementService.getAllPositions();
    res.json({ positions });
  } catch (error) {
    console.error('Get positions endpoint error:', error);
    res.status(500).json({ error: 'Failed to fetch positions' });
  }
});

// Get position by ID
router.get('/positions/:id', async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const position = await ManagementService.getPositionById(id);
    
    if (!position) {
      return res.status(404).json({ error: 'Position not found' });
    }

    res.json({ position });
  } catch (error) {
    console.error('Get position by ID endpoint error:', error);
    res.status(500).json({ error: 'Failed to fetch position' });
  }
});

// Create position (Admin only)
router.post('/positions', requirePermission(PERMISSIONS.POSITION_CREATE), async (req: AuthRequest, res: Response) => {
  try {
    const positionData = {
      name: req.body.name,
      description: req.body.description,
      department_id: req.body.departmentId,
      role_id: req.body.roleId,
      is_active: true,
    };

    const position = await ManagementService.createPosition(positionData);

    res.status(201).json({
      message: 'Position created successfully',
      position
    });
  } catch (error) {
    console.error('Create position endpoint error:', error);
    
    if (error instanceof Error && error.message.includes('already exists')) {
      return res.status(409).json({ error: error.message });
    }
    
    res.status(500).json({ error: 'Failed to create position' });
  }
});

// Update position (Admin only)
router.put('/positions/:id', requirePermission(PERMISSIONS.POSITION_UPDATE), async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    
    const updateData: any = {};
    if (req.body.name !== undefined) updateData.name = req.body.name;
    if (req.body.description !== undefined) updateData.description = req.body.description;
    if (req.body.departmentId !== undefined) updateData.department_id = req.body.departmentId;
    if (req.body.roleId !== undefined) updateData.role_id = req.body.roleId;
    if (req.body.isActive !== undefined) updateData.is_active = req.body.isActive;

    const position = await ManagementService.updatePosition(id, updateData);

    res.json({
      message: 'Position updated successfully',
      position
    });
  } catch (error) {
    console.error('Update position endpoint error:', error);
    
    if (error instanceof Error) {
      if (error.message === 'Position not found') {
        return res.status(404).json({ error: error.message });
      }
      if (error.message.includes('already exists')) {
        return res.status(409).json({ error: error.message });
      }
    }
    
    res.status(500).json({ error: 'Failed to update position' });
  }
});

// Department hierarchy and management
router.get('/departments/hierarchy', requirePermission(PERMISSIONS.DEPARTMENT_READ), async (req: AuthRequest, res: Response) => {
  try {
    const hierarchy = await ManagementService.getDepartmentHierarchy();
    res.json({ hierarchy });
  } catch (error) {
    console.error('Get department hierarchy endpoint error:', error);
    res.status(500).json({ error: 'Failed to fetch department hierarchy' });
  }
});

router.get('/departments/:id/subdepartments', requirePermission(PERMISSIONS.DEPARTMENT_READ), async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const departments = await ManagementService.getDepartmentWithSubdepartments(id);
    res.json({ departments });
  } catch (error) {
    console.error('Get department subdepartments endpoint error:', error);
    res.status(500).json({ error: 'Failed to fetch department subdepartments' });
  }
});

router.post('/departments/:id/assign-manager', requirePermission(PERMISSIONS.DEPARTMENT_UPDATE), async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const { managerId } = req.body;

    await ManagementService.assignDepartmentManager(id, managerId);
    res.json({ message: 'Manager assigned successfully' });
  } catch (error) {
    console.error('Assign department manager endpoint error:', error);
    res.status(500).json({ error: error instanceof Error ? error.message : 'Failed to assign manager' });
  }
});

router.delete('/departments/:id/manager', requirePermission(PERMISSIONS.DEPARTMENT_UPDATE), async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;

    await ManagementService.removeDepartmentManager(id);
    res.json({ message: 'Manager removed successfully' });
  } catch (error) {
    console.error('Remove department manager endpoint error:', error);
    res.status(500).json({ error: 'Failed to remove manager' });
  }
});

router.get('/departments/:id/employees', requirePermission(PERMISSIONS.EMPLOYEE_READ), async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const employees = await ManagementService.getDepartmentEmployees(id);
    res.json({ employees });
  } catch (error) {
    console.error('Get department employees endpoint error:', error);
    res.status(500).json({ error: 'Failed to fetch department employees' });
  }
});

// Organizational structure
router.get('/organization/chart', requirePermission(PERMISSIONS.DEPARTMENT_READ), async (req: AuthRequest, res: Response) => {
  try {
    const chart = await ManagementService.getOrganizationalChart();
    res.json(chart);
  } catch (error) {
    console.error('Get organizational chart endpoint error:', error);
    res.status(500).json({ error: 'Failed to fetch organizational chart' });
  }
});

router.get('/organization/stats', requirePermission(PERMISSIONS.SYSTEM_STATS), async (req: AuthRequest, res: Response) => {
  try {
    const stats = await ManagementService.getOrganizationalStats();
    res.json({ stats });
  } catch (error) {
    console.error('Get organizational stats endpoint error:', error);
    res.status(500).json({ error: 'Failed to fetch organizational statistics' });
  }
});

router.get('/managers/:id/subordinates', requirePermission(PERMISSIONS.EMPLOYEE_READ), async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const subordinates = await ManagementService.getManagerSubordinates(id);
    res.json({ subordinates });
  } catch (error) {
    console.error('Get manager subordinates endpoint error:', error);
    res.status(500).json({ error: 'Failed to fetch manager subordinates' });
  }
});

// Position hierarchy and management
router.get('/positions/hierarchy', requirePermission(PERMISSIONS.POSITION_READ), async (req: AuthRequest, res: Response) => {
  try {
    const hierarchy = await ManagementService.getPositionHierarchy();
    res.json({ hierarchy });
  } catch (error) {
    console.error('Get position hierarchy endpoint error:', error);
    res.status(500).json({ error: 'Failed to fetch position hierarchy' });
  }
});

router.get('/positions/department/:departmentId', requirePermission(PERMISSIONS.POSITION_READ), async (req: AuthRequest, res: Response) => {
  try {
    const { departmentId } = req.params;
    const positions = await ManagementService.getPositionsByDepartment(departmentId);
    res.json({ positions });
  } catch (error) {
    console.error('Get positions by department endpoint error:', error);
    res.status(500).json({ error: 'Failed to fetch positions by department' });
  }
});

router.get('/positions/:id/employees', requirePermission(PERMISSIONS.EMPLOYEE_READ), async (req: AuthRequest, res: Response) => {
  try {
    const { id } = req.params;
    const employees = await ManagementService.getPositionEmployees(id);
    res.json({ employees });
  } catch (error) {
    console.error('Get position employees endpoint error:', error);
    res.status(500).json({ error: 'Failed to fetch position employees' });
  }
});

// Advanced employee search
router.get('/employees/search', requirePermission(PERMISSIONS.EMPLOYEE_READ), async (req: AuthRequest, res: Response) => {
  try {
    const searchParams = {
      query: req.query.q as string,
      department: req.query.department as string,
      position: req.query.position as string,
      role: req.query.role as string,
      status: req.query.status as string,
      limit: parseInt(req.query.limit as string) || 20,
      offset: parseInt(req.query.offset as string) || 0,
    };

    const result = await ManagementService.searchEmployees(searchParams);
    res.json(result);
  } catch (error) {
    console.error('Search employees endpoint error:', error);
    res.status(500).json({ error: 'Failed to search employees' });
  }
});

export default router;
