-- Fix all role permissions to match the DEFAULT_ROLE_PERMISSIONS from the code

-- Update manager role permissions
UPDATE roles 
SET permissions = '[
  "user:read",
  "user:update",
  "user:status_change",
  "employee:create",
  "employee:read",
  "employee:update",
  "employee:delete",
  "employee:bulk",
  "department:read",
  "position:read",
  "role:read",
  "schedule:create",
  "schedule:read",
  "schedule:update",
  "schedule:delete",
  "schedule:assign",
  "leave:create",
  "leave:read",
  "leave:update",
  "leave:delete",
  "leave:approve",
  "reports:view",
  "reports:export"
]'::jsonb
WHERE name = 'manager';

-- Update doctor role permissions
UPDATE roles 
SET permissions = '[
  "employee:read",
  "schedule:read",
  "leave:create",
  "leave:read",
  "leave:update",
  "reports:view"
]'::jsonb
WHERE name = 'doctor';

-- Update nurse role permissions
UPDATE roles 
SET permissions = '[
  "employee:read",
  "schedule:read",
  "leave:create",
  "leave:read",
  "leave:update"
]'::jsonb
WHERE name = 'nurse';

-- Update pharmacist role permissions
UPDATE roles 
SET permissions = '[
  "employee:read",
  "schedule:read",
  "leave:create",
  "leave:read",
  "leave:update"
]'::jsonb
WHERE name = 'pharmacist';

-- Update technician role permissions
UPDATE roles 
SET permissions = '[
  "employee:read",
  "schedule:read",
  "leave:create",
  "leave:read",
  "leave:update"
]'::jsonb
WHERE name = 'technician';

-- Update receptionist role permissions
UPDATE roles 
SET permissions = '[
  "employee:read",
  "schedule:read",
  "leave:create",
  "leave:read",
  "leave:update"
]'::jsonb
WHERE name = 'receptionist';

-- Verify all role permissions
SELECT name, permissions, is_active FROM roles ORDER BY name;
