import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Textarea } from '@/components/ui/textarea';
import { usePermissions } from '@/hooks/usePermissions';
import { useUserManagement } from '@/hooks/useUserManagement';
import { 
  Shield, 
  CheckCircle, 
  XCircle, 
  Search, 
  User,
  AlertTriangle,
  Info
} from 'lucide-react';

export function PermissionChecker() {
  const [selectedUserId, setSelectedUserId] = useState('');
  const [singlePermission, setSinglePermission] = useState('');
  const [multiplePermissions, setMultiplePermissions] = useState('');
  const [validationPermissions, setValidationPermissions] = useState('');
  const [checkResults, setCheckResults] = useState<any>(null);

  const { 
    checkPermission, 
    checkAnyPermission, 
    validatePermissions,
    useMyPermissions,
    useUserPermissions,
    isCheckingPermission,
    isCheckingAnyPermission,
    isValidatingPermissions
  } = usePermissions();

  const { useUsers } = useUserManagement();
  const { data: usersData } = useUsers({ limit: 100 });
  const { data: myPermissions } = useMyPermissions();
  const { data: userPermissions } = useUserPermissions(selectedUserId);

  const users = usersData?.users || [];

  const handleSinglePermissionCheck = async () => {
    if (!singlePermission.trim()) return;

    try {
      const result = await checkPermission({ 
        permission: singlePermission.trim(), 
        userId: selectedUserId || undefined 
      });
      setCheckResults({
        type: 'single',
        permission: singlePermission.trim(),
        userId: selectedUserId,
        result
      });
    } catch (error) {
      console.error('Permission check error:', error);
    }
  };

  const handleMultiplePermissionCheck = async () => {
    if (!multiplePermissions.trim()) return;

    const permissions = multiplePermissions
      .split('\n')
      .map(p => p.trim())
      .filter(p => p.length > 0);

    try {
      const result = await checkAnyPermission({ 
        permissions, 
        userId: selectedUserId || undefined 
      });
      setCheckResults({
        type: 'multiple',
        permissions,
        userId: selectedUserId,
        result
      });
    } catch (error) {
      console.error('Permission check error:', error);
    }
  };

  const handleValidatePermissions = async () => {
    if (!validationPermissions.trim()) return;

    const permissions = validationPermissions
      .split('\n')
      .map(p => p.trim())
      .filter(p => p.length > 0);

    try {
      const result = await validatePermissions(permissions);
      setCheckResults({
        type: 'validation',
        permissions,
        result
      });
    } catch (error) {
      console.error('Permission validation error:', error);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold">Permission Checker</h2>
        <p className="text-gray-600">Test dan validasi izin pengguna</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* User Selection */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Pilih Pengguna
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="userSelect">Pengguna (kosongkan untuk pengguna saat ini)</Label>
              <Select value={selectedUserId} onValueChange={setSelectedUserId}>
                <SelectTrigger>
                  <SelectValue placeholder="Pilih pengguna atau kosongkan untuk diri sendiri" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Pengguna Saat Ini</SelectItem>
                  {users.map((user: any) => (
                    <SelectItem key={user.id} value={user.id}>
                      {user.first_name} {user.last_name} ({user.email})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Current User Permissions */}
            {!selectedUserId && myPermissions && (
              <div>
                <Label>Izin Anda Saat Ini</Label>
                <div className="mt-2 max-h-32 overflow-y-auto border rounded p-2">
                  <div className="flex flex-wrap gap-1">
                    {myPermissions.permissions.map((permission: string) => (
                      <Badge key={permission} variant="outline" className="text-xs">
                        {permission}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Selected User Permissions */}
            {selectedUserId && userPermissions && (
              <div>
                <Label>Izin Pengguna Terpilih</Label>
                <div className="mt-2 max-h-32 overflow-y-auto border rounded p-2">
                  <div className="flex flex-wrap gap-1">
                    {userPermissions.permissions.map((permission: string) => (
                      <Badge key={permission} variant="outline" className="text-xs">
                        {permission}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Permission Tests */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Test Izin
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Single Permission Check */}
            <div className="space-y-2">
              <Label htmlFor="singlePermission">Cek Izin Tunggal</Label>
              <div className="flex gap-2">
                <Input
                  id="singlePermission"
                  value={singlePermission}
                  onChange={(e) => setSinglePermission(e.target.value)}
                  placeholder="Contoh: user:create"
                />
                <Button 
                  onClick={handleSinglePermissionCheck}
                  disabled={isCheckingPermission || !singlePermission.trim()}
                >
                  {isCheckingPermission ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  ) : (
                    <Search className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>

            {/* Multiple Permission Check */}
            <div className="space-y-2">
              <Label htmlFor="multiplePermissions">Cek Beberapa Izin (salah satu)</Label>
              <Textarea
                id="multiplePermissions"
                value={multiplePermissions}
                onChange={(e) => setMultiplePermissions(e.target.value)}
                placeholder="user:create&#10;user:update&#10;user:delete"
                rows={3}
              />
              <Button 
                onClick={handleMultiplePermissionCheck}
                disabled={isCheckingAnyPermission || !multiplePermissions.trim()}
                className="w-full"
              >
                {isCheckingAnyPermission ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Mengecek...
                  </>
                ) : (
                  <>
                    <Search className="h-4 w-4 mr-2" />
                    Cek Izin
                  </>
                )}
              </Button>
            </div>

            {/* Permission Validation */}
            <div className="space-y-2">
              <Label htmlFor="validationPermissions">Validasi Izin</Label>
              <Textarea
                id="validationPermissions"
                value={validationPermissions}
                onChange={(e) => setValidationPermissions(e.target.value)}
                placeholder="user:create&#10;invalid:permission&#10;employee:read"
                rows={3}
              />
              <Button 
                onClick={handleValidatePermissions}
                disabled={isValidatingPermissions || !validationPermissions.trim()}
                className="w-full"
                variant="outline"
              >
                {isValidatingPermissions ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary mr-2"></div>
                    Memvalidasi...
                  </>
                ) : (
                  <>
                    <Shield className="h-4 w-4 mr-2" />
                    Validasi Izin
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Results */}
      {checkResults && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Info className="h-5 w-5" />
              Hasil Test
            </CardTitle>
          </CardHeader>
          <CardContent>
            {checkResults.type === 'single' && (
              <Alert>
                <div className="flex items-center gap-2">
                  {checkResults.result.hasPermission ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-500" />
                  )}
                  <AlertDescription>
                    <strong>Izin:</strong> {checkResults.permission}<br />
                    <strong>Pengguna:</strong> {checkResults.userId || 'Pengguna saat ini'}<br />
                    <strong>Hasil:</strong> {checkResults.result.hasPermission ? 'DIIZINKAN' : 'DITOLAK'}
                  </AlertDescription>
                </div>
              </Alert>
            )}

            {checkResults.type === 'multiple' && (
              <Alert>
                <div className="flex items-center gap-2">
                  {checkResults.result.hasAnyPermission ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-500" />
                  )}
                  <AlertDescription>
                    <strong>Izin yang dicek:</strong><br />
                    {checkResults.permissions.map((p: string, i: number) => (
                      <Badge key={i} variant="outline" className="mr-1 mb-1">{p}</Badge>
                    ))}<br />
                    <strong>Pengguna:</strong> {checkResults.userId || 'Pengguna saat ini'}<br />
                    <strong>Hasil:</strong> {checkResults.result.hasAnyPermission ? 'MEMILIKI MINIMAL SATU IZIN' : 'TIDAK MEMILIKI IZIN APAPUN'}
                  </AlertDescription>
                </div>
              </Alert>
            )}

            {checkResults.type === 'validation' && (
              <div className="space-y-4">
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Hasil Validasi:</strong> {checkResults.result.isValid ? 'Semua izin valid' : 'Ada izin yang tidak valid'}
                  </AlertDescription>
                </Alert>

                {checkResults.result.valid.length > 0 && (
                  <div>
                    <Label className="text-green-600">Izin Valid:</Label>
                    <div className="mt-2 flex flex-wrap gap-1">
                      {checkResults.result.valid.map((permission: string, i: number) => (
                        <Badge key={i} className="bg-green-100 text-green-800">
                          {permission}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {checkResults.result.invalid.length > 0 && (
                  <div>
                    <Label className="text-red-600">Izin Tidak Valid:</Label>
                    <div className="mt-2 flex flex-wrap gap-1">
                      {checkResults.result.invalid.map((permission: string, i: number) => (
                        <Badge key={i} className="bg-red-100 text-red-800">
                          {permission}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
