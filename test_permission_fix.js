// Direct database test to verify permission fix
import pkg from 'pg';
const { Pool } = pkg;
import bcrypt from 'bcryptjs';

// Database configuration
const pool = new Pool({
  host: 'localhost',
  port: 5432,
  database: 'manaj<PERSON><PERSON>_<PERSON><PERSON><PERSON>',
  user: 'postgres',
  password: '11<PERSON><PERSON><PERSON>'
});

async function testPermissionFix() {
  try {
    console.log('🧪 Testing Permission Fix - Direct Database Approach\n');

    // Test 1: Verify admin role has permissions
    console.log('1. Checking admin role permissions...');
    const roleResult = await pool.query(
      'SELECT name, permissions, is_active FROM roles WHERE name = $1',
      ['admin']
    );

    if (roleResult.rows.length === 0) {
      console.log('❌ Admin role not found');
      return;
    }

    const adminRole = roleResult.rows[0];
    const permissions = adminRole.permissions || [];
    console.log(`✅ Admin role found with ${permissions.length} permissions`);
    console.log(`   Active: ${adminRole.is_active}`);
    
    if (permissions.length === 0) {
      console.log('❌ Admin role has no permissions!');
      return;
    }

    // Test 2: Check if admin users exist
    console.log('\n2. Checking admin users...');
    const adminUsersResult = await pool.query(`
      SELECT u.id, u.email, p.role 
      FROM users u 
      LEFT JOIN profiles p ON u.id = p.id 
      WHERE p.role = 'admin'
    `);

    console.log(`✅ Found ${adminUsersResult.rows.length} admin users:`);
    adminUsersResult.rows.forEach(user => {
      console.log(`   - ${user.email} (${user.id})`);
    });

    // Test 3: Create a test admin user with known password
    console.log('\n3. Creating test admin user...');
    const testEmail = '<EMAIL>';
    const testPassword = 'TestAdmin123!';
    
    // Hash password
    const passwordHash = await bcrypt.hash(testPassword, 12);
    
    try {
      // Create user
      const userResult = await pool.query(`
        INSERT INTO users (id, email, password_hash, email_confirmed, created_at, updated_at)
        VALUES (gen_random_uuid(), $1, $2, true, NOW(), NOW())
        RETURNING id, email
        ON CONFLICT (email) DO UPDATE SET 
          password_hash = EXCLUDED.password_hash,
          email_confirmed = true
        RETURNING id, email
      `, [testEmail, passwordHash]);

      const userId = userResult.rows[0].id;
      
      // Create profile
      await pool.query(`
        INSERT INTO profiles (id, first_name, last_name, email, role, created_at, updated_at)
        VALUES ($1, 'Test', 'Admin', $2, 'admin', NOW(), NOW())
        ON CONFLICT (id) DO UPDATE SET 
          role = 'admin',
          first_name = 'Test',
          last_name = 'Admin'
      `, [userId, testEmail]);

      console.log(`✅ Test admin user created: ${testEmail}`);
      console.log(`   Password: ${testPassword}`);
      console.log(`   User ID: ${userId}`);

    } catch (error) {
      if (error.message.includes('duplicate key')) {
        console.log('ℹ️  Test admin user already exists');
      } else {
        console.log('❌ Failed to create test admin user:', error.message);
      }
    }

    // Test 4: Verify permission system works
    console.log('\n4. Testing permission system...');
    
    // Get a test admin user
    const testUserResult = await pool.query(`
      SELECT u.id, u.email, p.role 
      FROM users u 
      LEFT JOIN profiles p ON u.id = p.id 
      WHERE p.role = 'admin' 
      LIMIT 1
    `);

    if (testUserResult.rows.length === 0) {
      console.log('❌ No admin users found for testing');
      return;
    }

    const testUser = testUserResult.rows[0];
    
    // Test permission check query (simulating the PermissionService.userHasPermission)
    const permissionCheckResult = await pool.query(`
      SELECT r.permissions 
      FROM users u
      JOIN profiles p ON u.id = p.id
      JOIN roles r ON r.name = p.role
      WHERE u.id = $1 AND r.is_active = true
    `, [testUser.id]);

    if (permissionCheckResult.rows.length === 0) {
      console.log('❌ Permission check failed - no role found');
      return;
    }

    const userPermissions = permissionCheckResult.rows[0].permissions || [];
    console.log(`✅ Permission check successful for ${testUser.email}`);
    console.log(`   User has ${userPermissions.length} permissions`);
    
    // Check for specific admin permissions
    const requiredPermissions = ['user:create', 'user:read', 'system:admin', 'role:create'];
    const hasRequiredPermissions = requiredPermissions.every(perm => 
      userPermissions.includes(perm)
    );
    
    if (hasRequiredPermissions) {
      console.log('✅ Admin user has all required permissions');
    } else {
      console.log('❌ Admin user missing some required permissions');
      const missing = requiredPermissions.filter(perm => !userPermissions.includes(perm));
      console.log(`   Missing: ${missing.join(', ')}`);
    }

    console.log('\n🎉 Permission fix verification completed!');
    console.log('\n📋 Summary:');
    console.log(`   - Admin role permissions: ${permissions.length}`);
    console.log(`   - Admin users found: ${adminUsersResult.rows.length}`);
    console.log(`   - Permission system: ${hasRequiredPermissions ? 'Working' : 'Needs attention'}`);
    
    if (hasRequiredPermissions) {
      console.log('\n✅ PERMISSION FIX SUCCESSFUL!');
      console.log('   Administrators should now be able to access management features.');
    } else {
      console.log('\n❌ PERMISSION FIX INCOMPLETE');
      console.log('   Additional work needed on permission assignments.');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    await pool.end();
  }
}

// Run the test
testPermissionFix();
