-- Setup all roles with proper permissions
-- This script creates missing roles and assigns appropriate permissions

-- Insert missing roles if they don't exist
INSERT INTO roles (name, description, permissions, is_active) VALUES
('manager', 'Department Manager', '[]'::jsonb, true),
('doctor', 'Medical Doctor', '[]'::jsonb, true),
('nurse', 'Registered Nurse', '[]'::jsonb, true),
('pharmacist', 'Licensed Pharmacist', '[]'::jsonb, true),
('technician', 'Medical Technician', '[]'::jsonb, true),
('receptionist', 'Front Desk Receptionist', '[]'::jsonb, true)
ON CONFLICT (name) DO NOTHING;

-- Update manager role permissions
UPDATE roles 
SET permissions = '[
  "user:read",
  "user:update",
  "user:status_change",
  "employee:create",
  "employee:read",
  "employee:update",
  "employee:delete",
  "employee:bulk",
  "department:read",
  "position:read",
  "role:read",
  "schedule:create",
  "schedule:read",
  "schedule:update",
  "schedule:delete",
  "schedule:assign",
  "leave:create",
  "leave:read",
  "leave:update",
  "leave:delete",
  "leave:approve",
  "reports:view",
  "reports:export"
]'::jsonb
WHERE name = 'manager';

-- Update doctor role permissions
UPDATE roles 
SET permissions = '[
  "employee:read",
  "schedule:read",
  "leave:create",
  "leave:read",
  "leave:update",
  "reports:view"
]'::jsonb
WHERE name = 'doctor';

-- Update nurse role permissions
UPDATE roles 
SET permissions = '[
  "employee:read",
  "schedule:read",
  "leave:create",
  "leave:read",
  "leave:update"
]'::jsonb
WHERE name = 'nurse';

-- Update pharmacist role permissions
UPDATE roles 
SET permissions = '[
  "employee:read",
  "schedule:read",
  "leave:create",
  "leave:read",
  "leave:update"
]'::jsonb
WHERE name = 'pharmacist';

-- Update technician role permissions
UPDATE roles 
SET permissions = '[
  "employee:read",
  "schedule:read",
  "leave:create",
  "leave:read",
  "leave:update"
]'::jsonb
WHERE name = 'technician';

-- Update receptionist role permissions
UPDATE roles 
SET permissions = '[
  "employee:read",
  "schedule:read",
  "leave:create",
  "leave:read",
  "leave:update"
]'::jsonb
WHERE name = 'receptionist';

-- Verify all role permissions
SELECT name, 
       CASE 
         WHEN jsonb_array_length(permissions) > 5 THEN 
           jsonb_array_length(permissions) || ' permissions'
         ELSE permissions::text
       END as permissions_summary,
       is_active 
FROM roles 
ORDER BY name;
