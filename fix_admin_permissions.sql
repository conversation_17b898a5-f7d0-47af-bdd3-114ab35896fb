-- Fix admin role permissions
-- This script updates the admin role to have all required permissions

UPDATE roles 
SET permissions = '[
  "user:create",
  "user:read", 
  "user:update",
  "user:delete",
  "user:bulk",
  "user:password_reset",
  "user:status_change",
  "employee:create",
  "employee:read",
  "employee:update", 
  "employee:delete",
  "employee:bulk",
  "department:create",
  "department:read",
  "department:update",
  "department:delete",
  "role:create",
  "role:read",
  "role:update",
  "role:delete",
  "position:create",
  "position:read",
  "position:update",
  "position:delete",
  "schedule:create",
  "schedule:read",
  "schedule:update",
  "schedule:delete",
  "schedule:assign",
  "leave:create",
  "leave:read",
  "leave:update",
  "leave:delete",
  "leave:approve",
  "system:admin",
  "system:stats",
  "system:logs",
  "system:backup",
  "reports:view",
  "reports:export",
  "reports:analytics"
]'::jsonb
WHERE name = 'admin';

-- Verify the update
SELECT name, permissions, is_active FROM roles WHERE name = 'admin';
